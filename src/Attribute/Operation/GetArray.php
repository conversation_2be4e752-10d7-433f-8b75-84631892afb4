<?php

declare(strict_types=1);

namespace Pre<PERSON>ero\ApiBundle\Attribute\Operation;

use Pre<PERSON><PERSON>\ApiBundle\Attribute\Value\Filter;
use Pre<PERSON>ero\ApiBundle\Attribute\Value\Header;
use Pre<PERSON>ero\ApiBundle\Attribute\Value\PathParameter;
use Pre<PERSON>ero\ApiBundle\Attribute\Value\QueryParameter;
use PreZero\ApiBundle\Attribute\Value\Response;
use PreZero\ApiBundle\Enum\ContentType;
use PreZero\ApiBundle\Enum\Pagination;

class GetArray extends Operation
{
    public const string HTTP_METHOD = 'GET';

    /**
     * @param array<string, string>     $urlRequirements
     * @param array<PathParameter>|null $pathParameters
     * @param array<QueryParameter>     $queryParameters
     * @param array<Response>           $responses
     * @param array<string, mixed>      $normalizationContext
     * @param array<string, mixed>      $denormalizationContext
     * @param array<Filter>             $filters
     * @param ?class-string             $input
     * @param ?class-string             $output
     * @param array<Header>             $additionalRequestHeaders
     * @param array<int>|null           $perPageOptions
     */
    public function __construct(
        string $controller,
        string $controllerAction,
        ?string $name = null,
        string $summary = '',
        string $description = '',
        ?string $security = null,
        int $priority = 0,

        // Request
        ?string $uriTemplate = null,
        array $urlRequirements = [],
        ?array $pathParameters = null,
        array $queryParameters = [],
        bool $omitDefaultQueryParameters = false,
        ContentType $requestType = ContentType::EMPTY,
        string $requestDescription = '',
        array $denormalizationContext = [],
        array $filters = [],
        ?string $input = null,
        array $additionalRequestHeaders = [],

        // Response
        Pagination $pagination = Pagination::NONE,
        ?array $perPageOptions = null,
        ?int $defaultPerPage = null,
        string $responseDescription = '',
        array $responses = [],
        ContentType $responseType = ContentType::DTO,
        array $normalizationContext = [],
        ?string $output = null,
        int $successHttpCode = 200,
        ?string $responseOpenApiSchemaName = null,
        public array $responseCodesLogLevel = [],
    ) {
        parent::__construct(
            controller: $controller,
            controllerAction: $controllerAction,
            name: $name,
            summary: $summary,
            description: $description,
            security: $security,
            priority: $priority,
            uriTemplate: $uriTemplate,
            urlRequirements: $urlRequirements,
            pathParameters: $pathParameters,
            queryParameters: $queryParameters,
            omitDefaultQueryParameters: $omitDefaultQueryParameters,
            requestType: $requestType,
            requestDescription: $requestDescription,
            denormalizationContext: $denormalizationContext,
            filters: $filters,
            input: $input,
            additionalRequestHeaders: $additionalRequestHeaders,
            requestOpenApiSchemaName: null,
            pagination: $pagination,
            perPageOptions: $perPageOptions,
            defaultPerPage: $defaultPerPage,
            responseDescription: $responseDescription,
            responses: $responses,
            responseType: $responseType,
            normalizationContext: $normalizationContext,
            output: $output,
            successHttpCode: $successHttpCode,
            responseOpenApiSchemaName: $responseOpenApiSchemaName,
            responseCodesLogLevel: $responseCodesLogLevel,
        );
    }
}
